"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormData } from "../page";
import { cn } from "@/lib/utils"; // for conditional styling (if you're using shadcn/ui)

interface GoodMoralFormProps {
  onSubmit: (data: FormData) => void;
  initialData: FormData;
}

export function GoodMoralForm({ onSubmit, initialData }: GoodMoralFormProps) {
  const [formData, setFormData] = useState<FormData>(initialData);
  const [imagePreview, setImagePreview] = useState<string>("");

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setImagePreview(result);
        handleInputChange("image", result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const requiredFields = [
      "firstName",
      "middleName",
      "lastName",
      "age",
      "province",
      "municipality",
      "postalAddress",
      "day",
      "month",
      "year",
      "mayor",
      "ctcNo",
      "orNo",
    ];

    const missing = requiredFields.filter(
      (f) => !formData[f as keyof FormData]
    );

    if (missing.length > 0) {
      alert(`Missing required fields: ${missing.join(", ")}`);
      return;
    }

    onSubmit(formData);
  };

  return (
    <div className="max-w-3xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">
            Good Moral Certificate Application
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Upload Area */}
            <div className="flex flex-col items-center space-y-2">
              <input
                id="image"
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                className="hidden"
              />
              <label
                htmlFor="image"
                className={cn(
                  "w-40 h-40 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center text-gray-500 text-sm text-center cursor-pointer hover:bg-gray-50 transition"
                )}
              >
                {imagePreview ? (
                  <img
                    src={imagePreview}
                    alt="Applicant"
                    className="w-full h-full object-cover rounded-md"
                  />
                ) : (
                  <span className="px-2">
                    Click to upload
                    <br />
                    2x2 photo
                  </span>
                )}
              </label>
            </div>

            {/* Personal Info */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-primary">
                Personal Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {["firstName", "middleName", "lastName"].map((field) => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>
                      {field.replace(/([A-Z])/g, " $1")}
                    </Label>
                    <Input
                      id={field}
                      value={formData[field as keyof FormData]}
                      onChange={(e) =>
                        handleInputChange(
                          field as keyof FormData,
                          e.target.value
                        )
                      }
                      required
                    />
                  </div>
                ))}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="age">Age</Label>
                  <Input
                    id="age"
                    type="number"
                    value={formData.age}
                    onChange={(e) => handleInputChange("age", e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="postalAddress">Barangay Address</Label>
                  <Input
                    id="postalAddress"
                    placeholder="e.g., Brgy. Mohon"
                    value={formData.postalAddress}
                    onChange={(e) =>
                      handleInputChange("postalAddress", e.target.value)
                    }
                    required
                  />
                </div>
              </div>
            </div>

            {/* Location Info */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-primary">
                Location Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {["province", "municipality"].map((field) => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>
                      {field.charAt(0).toUpperCase() + field.slice(1)}
                    </Label>
                    <Input
                      id={field}
                      placeholder={
                        field === "province" ? "e.g., Leyte" : "e.g., Tanauan"
                      }
                      value={formData[field as keyof FormData]}
                      onChange={(e) =>
                        handleInputChange(
                          field as keyof FormData,
                          e.target.value
                        )
                      }
                      required
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Document Info */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-primary">
                Document Details
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {["day", "month", "year"].map((field) => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>
                      {field.charAt(0).toUpperCase() + field.slice(1)}
                    </Label>
                    <Input
                      id={field}
                      type={
                        field === "day" || field === "year" ? "number" : "text"
                      }
                      value={formData[field as keyof FormData]}
                      onChange={(e) =>
                        handleInputChange(
                          field as keyof FormData,
                          e.target.value
                        )
                      }
                      required
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Official Info */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-primary">
                Official Use
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {["mayor", "ctcNo", "orNo"].map((field) => (
                  <div key={field} className="space-y-2">
                    <Label htmlFor={field}>
                      {field.toUpperCase().replace("NO", " No.")}
                    </Label>
                    <Input
                      id={field}
                      value={formData[field as keyof FormData]}
                      onChange={(e) =>
                        handleInputChange(
                          field as keyof FormData,
                          e.target.value
                        )
                      }
                      required
                    />
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-center pt-6">
              <Button type="submit" size="lg" className="px-8">
                Generate Certificate
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
