"use client";

import { useState } from "react";
import { GoodMoralForm, CertificatePreview } from "./components";

export interface FormData {
  firstName: string;
  middleName: string;
  lastName: string;
  age: string;
  province: string;
  municipality: string;
  barangayAddress: string; // Changed from postalAddress to match the design
  day: string;
  month: string;
  year: string;
  mayor: string;
  ctcNo: string;
  orNo: string;
  image: string;
}

export default function GoodMoralPage() {
  const [formData, setFormData] = useState<FormData>({
    firstName: "",
    middleName: "",
    lastName: "",
    age: "",
    province: "Leyte",
    municipality: "Tanauan",
    barangayAddress: "", // Changed from postalAddress
    day: new Date().getDate().toString(),
    month: new Date().toLocaleString("default", { month: "long" }),
    year: new Date().getFullYear().toString(),
    mayor: "",
    ctcNo: "",
    orNo: "",
    image: "",
  });

  const [showPreview, setShowPreview] = useState(false);

  const handleFormSubmit = (data: FormData) => {
    setFormData(data);
    setShowPreview(true);
  };

  const handleBackToForm = () => {
    setShowPreview(false);
  };

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      {!showPreview ? (
        <GoodMoralForm onSubmit={handleFormSubmit} initialData={formData} />
      ) : (
        <CertificatePreview
          formData={formData}
          onBack={handleBackToForm}
          onEdit={() => setShowPreview(false)}
        />
      )}
    </div>
  );
}
